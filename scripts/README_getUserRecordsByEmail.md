# Get User Records by <PERSON><PERSON>

This script retrieves all records from three MongoDB collections for a given email address:

1. `community_subscriptions`
2. `raw_transactions` 
3. `community_purchase_transaction`

## Usage

```bash
node scripts/getUserRecordsByEmail.js <email> [output_format]
```

### Parameters

- `<email>` (required): The email address to search for
- `[output_format]` (optional): Output format - either `json` (default) or `csv`

### Examples

```bash
# Basic usage with JSON output
node scripts/getUserRecordsByEmail.js <EMAIL>

# With JSON output explicitly specified
node scripts/getUserRecordsByEmail.js <EMAIL> json

# With CSV output
node scripts/getUserRecordsByEmail.js <EMAIL> csv
```

## Output

The script will:

1. **Display a summary** in the console showing:
   - Total number of records found in each collection
   - Detailed information for each record

2. **Save results to a file** in the `scripts/output/` directory:
   - JSON format: `user_records_<email>_<timestamp>.json`
   - CSV format: `user_records_<email>_<timestamp>.csv`

### Console Output Example

```
🔍 Searching for records with email: <EMAIL>

📊 SUMMARY
==================================================
Email: <EMAIL>
Community Subscriptions: 2 records
Raw Transactions: 5 records
Community Purchase Transactions: 3 records
Total Records Found: 10
==================================================

📋 COMMUNITY SUBSCRIPTIONS
------------------------------
1. Subscription ID: 20001
   Community Code: COMM123
   Status: CURRENT
   Created: 2024-01-15T10:30:00.000Z
   Learner ID: 12345

💰 RAW TRANSACTIONS
------------------------------
1. Transaction Type: INBOUND
   Purchase Type: SUBSCRIPTION
   Amount: 99.99 USD
   Payment Provider: stripe
   Status: completed
   Created: 2024-01-15T10:25:00.000Z
   Reference ID: txn_abc123

🛒 COMMUNITY PURCHASE TRANSACTIONS
------------------------------
1. Community Code: COMM123
   Amount: 99.99 USD
   Local Amount: 99.99 USD
   Requestor: web
   Status: completed
   Created: 2024-01-15T10:20:00.000Z
   Learner ID: 12345
```

## Features

- **Email validation**: Validates email format before processing
- **Error handling**: Graceful error handling with informative messages
- **Multiple output formats**: Supports both JSON and CSV export
- **Detailed logging**: Shows progress and results in the console
- **File output**: Automatically saves results to timestamped files
- **Summary statistics**: Provides count of records found in each collection

## Requirements

- Node.js environment with access to the MongoDB database
- Proper environment variables configured (`.env` file)
- MongoDB connection established

## Collections Searched

### community_subscriptions
- Fields displayed: subscriptionId, communityCode, status, createdAt, learnerId
- Search field: `email`

### raw_transactions  
- Fields displayed: transactionType, purchaseType, originalAmount, originalCurrency, paymentProvider, status, transactionCreatedAt, transactionReferenceId
- Search field: `email`

### community_purchase_transaction
- Fields displayed: community_code, amount, currency, local_amount, local_currency, requestor, status, createdAt, learnerId
- Search field: `email`

## Error Handling

The script handles various error scenarios:
- Missing email parameter
- Invalid email format
- Database connection issues
- Collection query errors
- File system errors

## Output Files

Files are saved in `scripts/output/` directory with the following naming convention:
- JSON: `user_records_<email_sanitized>_<timestamp>.json`
- CSV: `user_records_<email_sanitized>_<timestamp>.csv`

Where `<email_sanitized>` replaces `@` with `_at_` and `.` with `_` for filesystem compatibility.
