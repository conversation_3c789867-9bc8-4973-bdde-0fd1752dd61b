require('dotenv').config({
  path: process.env.NODE_ENV === 'test' ? '.env.test' : '.env',
});
const fs = require('fs');
const mongoClient = require('../src/mongoClient');

// Import the models
const CommunitySubscriptions = require('../src/communitiesAPI/models/communitySubscriptions.model');
const RawTransactions = require('../src/models/rawTransaction.model');
const CommunityPurchaseTransaction = require('../src/communitiesAPI/models/communityPurchaseTransactions.model');
const CommunityAddonTransactions = require('../src/communitiesAPI/models/communityAddonTransactions.model');
const Learners = require('../src/models/learners.model');
const CommunityRoles = require('../src/communitiesAPI/models/communityRole.model');

/**
 * Retrieve all records from community_subscriptions for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of community subscription records
 */
async function getCommunitySubscriptions(email) {
  try {
    const subscriptions = await CommunitySubscriptions.find(
      {
        email,
      },
      'communityCode email learnerId learnerObjectId amount currency memberType status subscriptionId'
    ).lean();
    return subscriptions;
  } catch (error) {
    console.error('Error fetching community subscriptions:', error);
    return [];
  }
}

/**
 * Retrieve all records from raw_transactions for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of raw transaction records
 */
async function getRawTransactions(email) {
  try {
    const transactions = await RawTransactions.find(
      { email },
      'paymentProvider transactionReferenceId amoountInUsd communityObjectId createdAt discountCode email entityObjectId learnerObjectId originalAmount originalCurrency purchaseType transactionType status'
    ).lean();
    return transactions;
  } catch (error) {
    console.error('Error fetching raw transactions:', error);
    return [];
  }
}

/**
 * Retrieve all records from community_purchase_transaction for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of community purchase transaction records
 */
async function getCommunityPurchaseTransactions(email) {
  try {
    const transactions = await CommunityPurchaseTransaction.find(
      {
        email,
      },
      'community_code email learnerId learnerObjectId currency amount local_currency local_amount payment_details'
    ).lean();
    return transactions;
  } catch (error) {
    console.error(
      'Error fetching community purchase transactions:',
      error
    );
    return [];
  }
}

/**
 * Retrieve all records from community_addon_transactions for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of community addon transaction records
 */
async function getCommunityAddonTransactions(email) {
  try {
    const transactions = await CommunityAddonTransactions.find({
      email,
    }).lean();
    return transactions;
  } catch (error) {
    console.error('Error fetching community addon transactions:', error);
    return [];
  }
}

/**
 * Retrieve all records from learners for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of learner records
 */
async function getLearners(email) {
  try {
    const learners = await Learners.find(
      { email },
      'learnerId firstName lastName email profileImage timezone countryCode phoneNumber isActive createdAt lastModifiedTimeStamp'
    ).lean();
    return learners;
  } catch (error) {
    console.error('Error fetching learners:', error);
    return [];
  }
}

/**
 * Retrieve all records from community_roles for a given email
 * @param {string} email - The email to search for
 * @returns {Promise<Array>} Array of community role records
 */
async function getCommunityRoles(email) {
  try {
    const roles = await CommunityRoles.find(
      { email },
      'userObjectId email communityObjectId communityCode role config createdAt lastModifiedTimeStamp'
    ).lean();
    return roles;
  } catch (error) {
    console.error('Error fetching community roles:', error);
    return [];
  }
}

/**
 * Get all records for a given email from all collections
 * @param {string} email - The email to search for
 * @returns {Promise<Object>} Object containing all records organized by collection
 */
async function getAllRecordsByEmail(email) {
  console.log(`\n🔍 Searching for records with email: ${email}\n`);

  const [
    subscriptions,
    rawTransactions,
    purchaseTransactions,
    addonTransactions,
    learners,
    communityRoles,
  ] = await Promise.all([
    getCommunitySubscriptions(email),
    getRawTransactions(email),
    getCommunityPurchaseTransactions(email),
    getCommunityAddonTransactions(email),
    getLearners(email),
    getCommunityRoles(email),
  ]);

  const results = {
    email,
    community_subscriptions: subscriptions,
    raw_transactions: rawTransactions,
    community_purchase_transactions: purchaseTransactions,
    community_addon_transactions: addonTransactions,
    learners,
    community_roles: communityRoles,
    summary: {
      community_subscriptions_count: subscriptions.length,
      raw_transactions_count: rawTransactions.length,
      community_purchase_transactions_count: purchaseTransactions.length,
      community_addon_transactions_count: addonTransactions.length,
      learners_count: learners.length,
      community_roles_count: communityRoles.length,
      total_records:
        subscriptions.length +
        rawTransactions.length +
        purchaseTransactions.length +
        addonTransactions.length +
        learners.length +
        communityRoles.length,
    },
  };

  return results;
}

/**
 * Display results in a formatted way
 * @param {Object} results - The results object from getAllRecordsByEmail
 */
function displayResults(results) {
  console.log('📊 SUMMARY');
  console.log('='.repeat(50));
  console.log(`Email: ${results.email}`);
  console.log(
    `Community Subscriptions: ${results.summary.community_subscriptions_count} records`
  );
  console.log(
    `Raw Transactions: ${results.summary.raw_transactions_count} records`
  );
  console.log(
    `Community Purchase Transactions: ${results.summary.community_purchase_transactions_count} records`
  );
  console.log(
    `Community Addon Transactions: ${results.summary.community_addon_transactions_count} records`
  );
  console.log(`Learners: ${results.summary.learners_count} records`);
  console.log(
    `Community Roles: ${results.summary.community_roles_count} records`
  );
  console.log(`Total Records Found: ${results.summary.total_records}`);
  console.log('='.repeat(50));

  if (results.community_subscriptions.length > 0) {
    console.log('\n📋 COMMUNITY SUBSCRIPTIONS');
    console.log('-'.repeat(30));
    results.community_subscriptions.forEach((sub, index) => {
      console.log(`${index + 1}. Subscription ID: ${sub.subscriptionId}`);
      console.log(`   Community Code: ${sub.communityCode}`);
      console.log(`   Status: ${sub.status}`);
      console.log(`   Created: ${sub.createdAt}`);
      console.log(`   Learner ID: ${sub.learnerId}`);
      console.log('');
    });
  }

  if (results.raw_transactions.length > 0) {
    console.log('\n💰 RAW TRANSACTIONS');
    console.log('-'.repeat(30));
    results.raw_transactions.forEach((txn, index) => {
      console.log(
        `${index + 1}. Transaction Type: ${txn.transactionType}`
      );
      console.log(`   Purchase Type: ${txn.purchaseType}`);
      console.log(
        `   Amount: ${txn.originalAmount} ${txn.originalCurrency}`
      );
      console.log(`   Payment Provider: ${txn.paymentProvider}`);
      console.log(`   Status: ${txn.status}`);
      console.log(`   Created: ${txn.transactionCreatedAt}`);
      console.log(`   Reference ID: ${txn.transactionReferenceId}`);
      console.log('');
    });
  }

  if (results.community_purchase_transactions.length > 0) {
    console.log('\n🛒 COMMUNITY PURCHASE TRANSACTIONS');
    console.log('-'.repeat(30));
    results.community_purchase_transactions.forEach((txn, index) => {
      console.log(`${index + 1}. Community Code: ${txn.community_code}`);
      console.log(`   Amount: ${txn.amount} ${txn.currency}`);
      console.log(
        `   Local Amount: ${txn.local_amount} ${txn.local_currency}`
      );
      console.log(`   Requestor: ${txn.requestor}`);
      console.log(`   Status: ${txn.status}`);
      console.log(`   Created: ${txn.createdAt}`);
      console.log(`   Learner ID: ${txn.learnerId}`);
      console.log('');
    });
  }

  if (results.community_addon_transactions.length > 0) {
    console.log('\n🔧 COMMUNITY ADDON TRANSACTIONS');
    console.log('-'.repeat(30));
    results.community_addon_transactions.forEach((txn, index) => {
      console.log(`${index + 1}. Transaction ID: ${txn._id}`);
      console.log(`   Email: ${txn.email}`);
      console.log(`   Created: ${txn.createdAt}`);
      console.log('');
    });
  }

  if (results.learners.length > 0) {
    console.log('\n👤 LEARNERS');
    console.log('-'.repeat(30));
    results.learners.forEach((learner, index) => {
      console.log(`${index + 1}. Learner ID: ${learner.learnerId}`);
      console.log(`   Name: ${learner.firstName} ${learner.lastName}`);
      console.log(`   Email: ${learner.email}`);
      console.log(`   Phone: ${learner.phoneNumber || 'N/A'}`);
      console.log(`   Country: ${learner.countryCode || 'N/A'}`);
      console.log(`   Timezone: ${learner.timezone || 'N/A'}`);
      console.log(`   Active: ${learner.isActive}`);
      console.log(`   Created: ${learner.createdAt}`);
      console.log('');
    });
  }

  if (results.community_roles.length > 0) {
    console.log('\n🎭 COMMUNITY ROLES');
    console.log('-'.repeat(30));
    results.community_roles.forEach((role, index) => {
      console.log(`${index + 1}. Community Code: ${role.communityCode}`);
      console.log(`   Email: ${role.email}`);
      console.log(`   Roles: ${role.role.join(', ')}`);
      console.log(`   User Object ID: ${role.userObjectId}`);
      console.log(`   Community Object ID: ${role.communityObjectId}`);
      console.log(`   Created: ${role.createdAt}`);
      console.log('');
    });
  }
}

/**
 * Main function to run the script
 */
async function main() {
  try {
    // Get email from command line arguments
    // const email = '<EMAIL>'; // correct
    const email = '<EMAIL>'; // wrong

    if (!email) {
      console.error('❌ Please provide an email address as an argument');
      process.exit(1);
    }
    // Connect to MongoDB
    await mongoClient.connect();
    console.log('✅ Connected to MongoDB');

    // Get all records
    const results = await getAllRecordsByEmail(email);

    // Display results
    displayResults(results);

    // Save results to file
    const timestamp = Date.now();
    const emailSafe = email.replace('@', '_at_').replace(/\./g, '_');

    // Ensure output directory exists
    if (!fs.existsSync('scripts/output')) {
      fs.mkdirSync('scripts/output', { recursive: true });
    }
    const jsonPath = `scripts/output/user_records_${emailSafe}_${timestamp}.json`;
    fs.writeFileSync(jsonPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 Results saved to JSON: ${jsonPath}`);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  } finally {
    console.log('\n✅ Script completed');
    process.exit(0);
  }
}

// Run the script
main();
